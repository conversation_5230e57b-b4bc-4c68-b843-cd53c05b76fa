import logging
from django.contrib.auth import get_user_model
from typing import Dict, Any

logger = logging.getLogger(__name__)

User = get_user_model()


class UserStatusValidator:
    """
    Utility class for validating user account status during authentication
    """

    # Error codes for different validation failures
    ERROR_CODES = {
        "ACCOUNT_DELETED": "ACCOUNT_DELETED",
        "ACCOUNT_INACTIVE": "ACCOUNT_INACTIVE",
        "ACCOUNT_LOCKED": "ACCOUNT_LOCKED",
        "EMAIL_NOT_VERIFIED": "EMAIL_NOT_VERIFIED",
    }

    @classmethod
    def validate_user_status(cls, user, log_violations: bool = False) -> Dict[str, Any]:
        """
        Validate user account status for authentication

        Args:
            user: User instance to validate
            log_violations: Whether to log validation violations

        Returns:
            dict: Validation result with is_valid, error_code, error_message, error_details
        """
        if not user:
            return {
                "is_valid": False,
                "error_code": "USER_NOT_FOUND",
                "error_message": "User not found",
                "error_details": "The specified user does not exist",
            }

        # Check if user account is deleted
        if user.is_deleted:
            if log_violations:
                logger.warning(f"Authentication blocked for deleted user: {user.email}")
            return {
                "is_valid": False,
                "error_code": cls.ERROR_CODES["ACCOUNT_DELETED"],
                "error_message": "Account has been deleted",
                "error_details": "This account has been permanently deleted and cannot be used for authentication",
            }

        # Check if user account is active
        if not user.is_active:
            if log_violations:
                logger.warning(
                    f"Authentication blocked for inactive user: {user.email}"
                )
            return {
                "is_valid": False,
                "error_code": cls.ERROR_CODES["ACCOUNT_INACTIVE"],
                "error_message": "Account is inactive",
                "error_details": "This account is inactive and cannot be used for authentication",
            }

        # Check if account is locked (using existing lockout system)
        if hasattr(user, "is_account_locked") and user.is_account_locked():
            if log_violations:
                logger.warning(f"Authentication blocked for locked user: {user.email}")
            return {
                "is_valid": False,
                "error_code": cls.ERROR_CODES["ACCOUNT_LOCKED"],
                "error_message": "Account is locked",
                "error_details": "This account has been locked due to multiple failed login attempts",
            }

        # All validations passed
        return {
            "is_valid": True,
            "error_code": None,
            "error_message": None,
            "error_details": None,
        }

    @classmethod
    def get_user_status_summary(cls, user) -> Dict[str, Any]:
        """
        Get a summary of user status for token validation

        Args:
            user: User instance

        Returns:
            dict: User status summary
        """
        if not user:
            return {}

        return {
            "is_active": user.is_active,
            "is_deleted": user.is_deleted,
            "is_mail_verified": user.is_mail_verified,
            "is_locked": hasattr(user, "is_account_locked")
            and user.is_account_locked(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
        }


def log_authentication_attempt(
    user,
    success: bool,
    method: str,
    ip_address: str = None,
    user_agent: str = None,
    error_code: str = None,
    additional_info: Dict = None,
):
    """
    Log authentication attempts using the security logging system

    Args:
        user: User instance (can be None for failed attempts)
        success: Whether the authentication was successful
        method: Authentication method (e.g., 'password', 'oauth2_token', 'jwt_token', 'session', 'token')
        ip_address: Client IP address
        user_agent: Client user agent string
        error_code: Error code for failed attempts
        additional_info: Additional information to log
    """
    try:
        # Import here to avoid circular imports
        from oauth2_auth.security_logger import security_logger

        # Prepare metadata
        metadata = {
            "success": success,
            "authentication_method": method,
            "user_id": user.id if user else None,
            "user_email": user.email if user else None,
            "user_role": user.role if user else None,
        }

        if error_code:
            metadata["error_code"] = error_code

        if additional_info:
            metadata.update(additional_info)

        # Determine event type and description
        if success:
            event_type = "authentication_success"
            description = f"Successful authentication via {method}"
        else:
            event_type = "authentication_failure"
            description = f"Failed authentication via {method}"
            if error_code:
                description += f" (Error: {error_code})"

        # Log the event
        security_logger.log_event(
            event_type=event_type,
            description=description,
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata=metadata,
        )

        # Also log using standard Django logging
        if success:
            logger.info(
                f"Authentication success for {user.email if user else 'unknown'} via {method} from {ip_address}"
            )
        else:
            logger.warning(
                f"Authentication failure for {user.email if user else 'unknown'} via {method} from {ip_address}: {error_code}"
            )

    except Exception as e:
        # Don't let logging errors break authentication
        logger.error(f"Failed to log authentication attempt: {str(e)}")
