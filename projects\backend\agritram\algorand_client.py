from beaker import client, localnet
from decouple import config

from .contract import app

# # Get accounts from the localnet
accounts = localnet.kmd.get_accounts()
sender = accounts[0]  # Admin account

# # Initialize Algod client
algod_client = localnet.get_algod_client()

# print("Connected to Algorand client.", algod_client.client.indexer)

# # Create a new app account
app_client = client.ApplicationClient(
    client=algod_client,
    app=app,
    app_id=int(config("SMART_CONTRACT")),
    sender=sender.address,
    signer=sender.signer,
)

ASA_DECIMALS = config("ASA_DECIMALS")
ALGORAND_ASSET_ID_KTT = int(config("ALGORAND_ASSET_ID_KTT"))
ALGORAND_ASSET_ID_KCT = int(config("ALGORAND_ASSET_ID_KCT"))

print(f"ASA_DECIMALS: {ASA_DECIMALS}")
print(f"ALGORAND_ASSET_ID_KTT: {ALGORAND_ASSET_ID_KTT}")
print(f"ALGORAND_ASSET_ID_KCT: {ALGORAND_ASSET_ID_KCT}")
print(f"Sender address: {sender.address}")
print(f"Smart contract ID: {app_client.app_id}")
print(f"Data Base Name: {config('DATABASE_NAME')}")
# app_id, app_address, txid = app_client.create()
# print(f"App ID: {app_id}")
# print(f"App Address: {app_address}")
