"""
OAuth2 Token Management and Cleanup System (Legacy Compatibility)
Provides backward compatibility for management commands while using new security services
"""

import logging
from datetime import timedelta
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from oauth2_provider.models import AccessToken, RefreshToken, Grant, Application
from .utils import log_security_event
from .email_service import email_service
from .token_invalidation_service import token_invalidation_service
from .config import oauth2_security_config

logger = logging.getLogger(__name__)
User = get_user_model()


class TokenManager:
    """
    Legacy token management system - refactored to use new security services
    """

    def __init__(self):
        # Use centralized configuration
        self.access_token_lifetime = timedelta(seconds=oauth2_security_config.ACCESS_TOKEN_LIFETIME)
        self.refresh_token_lifetime = timedelta(seconds=oauth2_security_config.REFRESH_TOKEN_LIFETIME)
        self.cleanup_batch_size = oauth2_security_config.TOKEN_CLEANUP_THRESHOLDS['cleanup_batch_size']
    
    def cleanup_expired_tokens(self) -> Dict[str, int]:
        """
        Clean up expired tokens and grants
        """
        try:
            now = timezone.now()
            results = {
                'access_tokens_deleted': 0,
                'refresh_tokens_deleted': 0,
                'grants_deleted': 0,
                'errors': 0
            }
            
            # Clean up expired access tokens
            try:
                expired_access_tokens = AccessToken.objects.filter(
                    expires__lt=now
                )[:self.cleanup_batch_size]
                
                access_token_count = expired_access_tokens.count()
                if access_token_count > 0:
                    expired_access_tokens.delete()
                    results['access_tokens_deleted'] = access_token_count
                    logger.info(f"Cleaned up {access_token_count} expired access tokens")
                    
            except Exception as e:
                logger.error(f"Error cleaning up access tokens: {str(e)}")
                results['errors'] += 1
            
            # Clean up expired refresh tokens
            try:
                # Calculate refresh token expiry based on creation time
                refresh_expiry_cutoff = now - self.refresh_token_lifetime
                expired_refresh_tokens = RefreshToken.objects.filter(
                    created__lt=refresh_expiry_cutoff
                )[:self.cleanup_batch_size]
                
                refresh_token_count = expired_refresh_tokens.count()
                if refresh_token_count > 0:
                    expired_refresh_tokens.delete()
                    results['refresh_tokens_deleted'] = refresh_token_count
                    logger.info(f"Cleaned up {refresh_token_count} expired refresh tokens")
                    
            except Exception as e:
                logger.error(f"Error cleaning up refresh tokens: {str(e)}")
                results['errors'] += 1
            
            # Clean up expired grants
            try:
                expired_grants = Grant.objects.filter(
                    expires__lt=now
                )[:self.cleanup_batch_size]
                
                grant_count = expired_grants.count()
                if grant_count > 0:
                    expired_grants.delete()
                    results['grants_deleted'] = grant_count
                    logger.info(f"Cleaned up {grant_count} expired grants")
                    
            except Exception as e:
                logger.error(f"Error cleaning up grants: {str(e)}")
                results['errors'] += 1
            
            # Log cleanup summary
            total_cleaned = (
                results['access_tokens_deleted'] + 
                results['refresh_tokens_deleted'] + 
                results['grants_deleted']
            )
            
            if total_cleaned > 0:
                log_security_event(
                    event_type='token_cleanup',
                    description=f'Token cleanup completed: {total_cleaned} items removed',
                    metadata=results
                )
            
            return results
            
        except Exception as e:
            logger.error(f"Token cleanup failed: {str(e)}")
            return {'error': str(e)}
    
    def revoke_user_tokens(self, user, reason: str = 'User request') -> bool:
        """
        Revoke all tokens for a specific user - uses new token invalidation service
        """
        try:
            # Use the new token invalidation service for enhanced security
            result = token_invalidation_service.invalidate_user_tokens(
                user=user,
                reason=reason
            )

            if 'error' in result:
                logger.error(f"Failed to revoke tokens for user {user.email}: {result['error']}")
                return False

            # Send notification email
            email_service.send_security_alert(
                user,
                'All Tokens Revoked',
                f'All your authentication tokens have been revoked. Reason: {reason}',
                {'reason': reason, 'total_tokens': result.get('total_invalidated', 0)}
            )

            logger.info(f"Revoked {result.get('total_invalidated', 0)} tokens for user {user.email}: {reason}")
            return True

        except Exception as e:
            logger.error(f"Failed to revoke tokens for user {user.email}: {str(e)}")
            return False
    
    def revoke_application_tokens(self, application: Application, reason: str = 'Application revoked') -> bool:
        """
        Revoke all tokens for a specific application
        """
        try:
            with transaction.atomic():
                # Count tokens before deletion
                access_count = AccessToken.objects.filter(application=application).count()
                refresh_count = RefreshToken.objects.filter(application=application).count()
                grant_count = Grant.objects.filter(application=application).count()
                
                # Delete all tokens
                AccessToken.objects.filter(application=application).delete()
                RefreshToken.objects.filter(application=application).delete()
                Grant.objects.filter(application=application).delete()
                
                # Log revocation
                log_security_event(
                    user=application.user,
                    event_type='application_tokens_revoked',
                    description=f'All application tokens revoked: {reason}',
                    metadata={
                        'application_id': application.id,
                        'client_id': application.client_id,
                        'reason': reason,
                        'access_tokens_revoked': access_count,
                        'refresh_tokens_revoked': refresh_count,
                        'grants_revoked': grant_count
                    }
                )
                
                logger.info(f"Revoked all tokens for application {application.client_id}: {reason}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to revoke tokens for application {application.client_id}: {str(e)}")
            return False
    
    def rotate_refresh_token(self, refresh_token: RefreshToken) -> Optional[RefreshToken]:
        """
        Rotate a refresh token (create new one, invalidate old one)
        DEPRECATED: Use jwt_rotation_service.refresh_access_token() instead
        """
        logger.warning("TokenManager.rotate_refresh_token() is deprecated. Use jwt_rotation_service.refresh_access_token() instead.")

        try:
            with transaction.atomic():
                # Create new refresh token
                new_refresh_token = RefreshToken.objects.create(
                    user=refresh_token.user,
                    application=refresh_token.application,
                    access_token=refresh_token.access_token
                )

                # Delete old refresh token
                old_token_id = refresh_token.id
                refresh_token.delete()

                # Log rotation
                log_security_event(
                    user=new_refresh_token.user,
                    event_type='refresh_token_rotated',
                    description='Refresh token rotated for security (legacy method)',
                    metadata={
                        'old_token_id': old_token_id,
                        'new_token_id': new_refresh_token.id,
                        'application_id': new_refresh_token.application.id,
                        'deprecated_method': True
                    }
                )

                logger.info(f"Rotated refresh token for user {new_refresh_token.user.email}")
                return new_refresh_token

        except Exception as e:
            logger.error(f"Failed to rotate refresh token: {str(e)}")
            return None
    
    def get_token_statistics(self) -> Dict:
        """
        Get comprehensive token statistics
        """
        try:
            now = timezone.now()
            
            # Access token stats
            total_access_tokens = AccessToken.objects.count()
            active_access_tokens = AccessToken.objects.filter(expires__gt=now).count()
            expired_access_tokens = total_access_tokens - active_access_tokens
            
            # Refresh token stats
            total_refresh_tokens = RefreshToken.objects.count()
            refresh_expiry_cutoff = now - self.refresh_token_lifetime
            active_refresh_tokens = RefreshToken.objects.filter(created__gt=refresh_expiry_cutoff).count()
            expired_refresh_tokens = total_refresh_tokens - active_refresh_tokens
            
            # Grant stats
            total_grants = Grant.objects.count()
            active_grants = Grant.objects.filter(expires__gt=now).count()
            expired_grants = total_grants - active_grants
            
            # Application stats
            total_applications = Application.objects.count()
            
            # User stats
            users_with_tokens = AccessToken.objects.values('user').distinct().count()
            
            return {
                'access_tokens': {
                    'total': total_access_tokens,
                    'active': active_access_tokens,
                    'expired': expired_access_tokens
                },
                'refresh_tokens': {
                    'total': total_refresh_tokens,
                    'active': active_refresh_tokens,
                    'expired': expired_refresh_tokens
                },
                'grants': {
                    'total': total_grants,
                    'active': active_grants,
                    'expired': expired_grants
                },
                'applications': {
                    'total': total_applications
                },
                'users': {
                    'with_active_tokens': users_with_tokens
                },
                'cleanup_recommendations': {
                    'expired_access_tokens': expired_access_tokens,
                    'expired_refresh_tokens': expired_refresh_tokens,
                    'expired_grants': expired_grants
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get token statistics: {str(e)}")
            return {'error': str(e)}
    
    def validate_token_security(self, access_token: AccessToken, request=None) -> Dict:
        """
        Validate token security and detect anomalies
        DEPRECATED: Use jwt_rotation_service.validate_access_token() and device_validation_service instead
        """
        logger.warning("TokenManager.validate_token_security() is deprecated. Use jwt_rotation_service.validate_access_token() and device_validation_service instead.")

        try:
            # Basic validation for legacy compatibility
            issues = []
            warnings = []

            # Check token age using centralized configuration
            token_age = timezone.now() - access_token.created
            max_token_age_hours = oauth2_security_config.ACCESS_TOKEN_LIFETIME // 3600  # Convert seconds to hours
            if token_age > timedelta(hours=max_token_age_hours * 24):  # Allow 24x the normal lifetime before warning
                warnings.append(f'Token is older than {max_token_age_hours * 24} hours')

            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'warnings': warnings,
                'token_age_hours': token_age.total_seconds() / 3600,
                'deprecated_method': True,
                'recommendation': 'Use jwt_rotation_service and device_validation_service for comprehensive security validation'
            }

        except Exception as e:
            logger.error(f"Token security validation failed: {str(e)}")
            return {'error': str(e)}


# Global token manager instance
token_manager = TokenManager()
