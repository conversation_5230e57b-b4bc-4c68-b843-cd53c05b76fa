from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


@receiver(pre_save, sender=User)
def track_user_status_changes(sender, instance, **kwargs):
    """
    Track changes to user status fields and log them for audit purposes.
    """
    if instance.pk:  # Only for existing users (updates)
        try:
            # Get the current state from database
            old_instance = User.objects.get(pk=instance.pk)

            # Track status field changes
            status_fields = [
                "is_active",
                "is_deleted",
                "is_mail_verified",
                "is_permanently_locked",
            ]

            changes = {}
            for field in status_fields:
                old_value = getattr(old_instance, field)
                new_value = getattr(instance, field)

                if old_value != new_value:
                    changes[field] = {
                        "old_value": old_value,
                        "new_value": new_value,
                        "changed_at": timezone.now(),
                    }

            # Log significant status changes
            if changes:
                # Store changes in instance for post_save signal
                instance._status_changes = changes

                # Log critical changes immediately
                critical_changes = []
                if "is_deleted" in changes and changes["is_deleted"]["new_value"]:
                    critical_changes.append("DELETED")
                if "is_active" in changes and not changes["is_active"]["new_value"]:
                    critical_changes.append("DEACTIVATED")
                if (
                    "is_permanently_locked" in changes
                    and changes["is_permanently_locked"]["new_value"]
                ):
                    critical_changes.append("PERMANENTLY_LOCKED")

                if critical_changes:
                    logger.warning(
                        f"Critical user status change for {instance.email}: "
                        f"{', '.join(critical_changes)} - Changes: {changes}"
                    )
                else:
                    logger.info(f"User status change for {instance.email}: {changes}")

        except User.DoesNotExist:
            # This shouldn't happen, but handle gracefully
            logger.error(
                f"Could not find existing user with pk {instance.pk} for status tracking"
            )
        except Exception as e:
            logger.error(
                f"Error tracking user status changes for {instance.email}: {e}"
            )


@receiver(post_save, sender=User)
def log_user_status_changes(sender, instance, created, **kwargs):
    """
    Log user status changes after save for comprehensive audit trail.
    """
    try:
        if created:
            # Log new user creation
            logger.info(
                f"New user created: {instance.email} "
                f"(Role: {instance.role}, Active: {instance.is_active}, "
                f"Email Verified: {instance.is_mail_verified})"
            )

            # Log if user is created in a restricted state
            restricted_states = []
            if not instance.is_active:
                restricted_states.append("INACTIVE")
            if instance.is_deleted:
                restricted_states.append("DELETED")

            if restricted_states:
                logger.warning(
                    f"User {instance.email} created with restricted status: "
                    f"{', '.join(restricted_states)}"
                )
        else:
            # Log status changes for existing users
            if hasattr(instance, "_status_changes"):
                changes = instance._status_changes

                # Create detailed audit log entry
                audit_entry = {
                    "user_id": instance.id,
                    "user_email": instance.email,
                    "timestamp": timezone.now().isoformat(),
                    "changes": changes,
                    "current_status": {
                        "is_active": instance.is_active,
                        "is_deleted": instance.is_deleted,
                        "is_mail_verified": instance.is_mail_verified,
                        "is_permanently_locked": instance.is_permanently_locked,
                    },
                }

                logger.info(f"User status audit: {audit_entry}")

                # Clean up the temporary attribute
                delattr(instance, "_status_changes")

    except Exception as e:
        logger.error(f"Error in post_save user status logging: {e}")


@receiver(pre_save, sender=User)
def validate_user_status_consistency(sender, instance, **kwargs):
    """
    Validate that user status fields are consistent and logical.
    """
    try:
        # Validation rules
        validation_errors = []

        # Deleted users should not be active
        if instance.is_deleted:
            if instance.is_active:
                instance.is_active = False
                logger.info(f"Auto-deactivating deleted user: {instance.email}")

        # Log validation corrections
        if validation_errors:
            logger.warning(
                f"User status validation corrections for {instance.email}: "
                f"{', '.join(validation_errors)}"
            )

    except Exception as e:
        logger.error(f"Error in user status validation: {e}")


# TODO:: Note: Online status tracking has been removed from the User model
# This signal handler is no longer needed but kept for reference
# @receiver(post_save, sender=User)
# def track_online_status_changes(sender, instance, created, **kwargs):
#     """
#     Track when users go online/offline for activity monitoring.
#     Note: is_online field has been removed from User model
#     """
#     pass
