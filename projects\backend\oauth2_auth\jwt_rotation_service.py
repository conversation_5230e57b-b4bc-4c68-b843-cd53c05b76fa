"""
JWT Token Rotation Service

Provides JWT token rotation with shorter lifespans and refresh token mechanism
for enhanced fintech-grade security.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import transaction
from oauth2_provider.models import Application, AccessToken, RefreshToken
from .utils import generate_jwt_token, validate_jwt_token, log_security_event, get_client_ip
from .token_invalidation_service import token_invalidation_service
from .config import oauth2_security_config

User = get_user_model()
logger = logging.getLogger(__name__)


class JWTRotationService:
    """
    Service for JWT token rotation and refresh mechanism
    """
    
    # Use centralized configuration
    ACCESS_TOKEN_LIFESPAN = oauth2_security_config.ACCESS_TOKEN_LIFETIME
    REFRESH_TOKEN_LIFESPAN = oauth2_security_config.REFRESH_TOKEN_LIFETIME

    # Cache settings for token tracking
    TOKEN_CACHE_PREFIX = "jwt_token_"
    REFRESH_CACHE_PREFIX = "refresh_token_"
    CACHE_TIMEOUT = oauth2_security_config.REFRESH_TOKEN_LIFETIME + 86400  # 1 day longer than refresh token
    
    @classmethod
    def generate_token_pair(cls, user, application, device_id=None, request=None) -> Dict[str, Any]:
        """
        Generate a new access token and refresh token pair
        
        Args:
            user: User instance
            application: OAuth2 application
            device_id: Device identifier (optional)
            request: HTTP request object (optional)
            
        Returns:
            dict: Token pair with metadata
        """
        try:
            with transaction.atomic():
                # Generate short-lived access token
                access_token = generate_jwt_token(
                    user=user,
                    application=application,
                    scope='read write profile email',
                    expires_in=cls.ACCESS_TOKEN_LIFESPAN,
                    device_id=device_id,
                    token_type='access'
                )
                
                # Generate long-lived refresh token
                refresh_token = generate_jwt_token(
                    user=user,
                    application=application,
                    scope='refresh',
                    expires_in=cls.REFRESH_TOKEN_LIFESPAN,
                    device_id=device_id,
                    token_type='refresh'
                )
                
                if not access_token or not refresh_token:
                    raise Exception("Failed to generate token pair")
                
                # Store token metadata in cache for tracking
                token_metadata = {
                    'user_id': user.id,
                    'device_id': device_id,
                    'application_id': application.id if hasattr(application, 'id') else None,
                    'created_at': timezone.now().isoformat(),
                    'ip_address': get_client_ip(request) if request else None
                }
                
                # Cache access token metadata
                access_cache_key = f"{cls.TOKEN_CACHE_PREFIX}{hash(access_token)}"
                cache.set(access_cache_key, token_metadata, cls.ACCESS_TOKEN_LIFESPAN + 300)  # 5 min buffer
                
                # Cache refresh token metadata
                refresh_cache_key = f"{cls.REFRESH_CACHE_PREFIX}{hash(refresh_token)}"
                cache.set(refresh_cache_key, token_metadata, cls.CACHE_TIMEOUT)
                
                # Log token generation
                log_security_event(
                    user=user,
                    event_type='jwt_token_pair_generated',
                    description='New JWT access and refresh token pair generated',
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                    device_id=device_id,
                    metadata={
                        'access_token_expires_in': cls.ACCESS_TOKEN_LIFESPAN,
                        'refresh_token_expires_in': cls.REFRESH_TOKEN_LIFESPAN,
                        'application_id': application.id if hasattr(application, 'id') else None
                    }
                )
                
                logger.info(f"Generated JWT token pair for user {user.email}")
                
                return {
                    'access_token': access_token,
                    'refresh_token': refresh_token,
                    'token_type': 'Bearer',
                    'expires_in': cls.ACCESS_TOKEN_LIFESPAN,
                    'refresh_expires_in': cls.REFRESH_TOKEN_LIFESPAN,
                    'scope': 'read write profile email'
                }
                
        except Exception as e:
            logger.error(f"Error generating JWT token pair: {str(e)}")
            return {'error': str(e)}
    
    @classmethod
    def refresh_access_token(cls, refresh_token: str, request=None) -> Dict[str, Any]:
        """
        Refresh an access token using a valid refresh token
        
        Args:
            refresh_token: Refresh token string
            request: HTTP request object (optional)
            
        Returns:
            dict: New token pair or error
        """
        try:
            # Validate refresh token
            payload = validate_jwt_token(refresh_token)
            if not payload:
                return {'error': 'Invalid refresh token'}
            
            # Check if it's actually a refresh token
            if payload.get('token_type') != 'refresh':
                return {'error': 'Token is not a refresh token'}
            
            # Get user
            try:
                user = User.objects.get(id=payload['sub'])
            except User.DoesNotExist:
                return {'error': 'User not found'}
            
            # Validate user status
            from user.auth_utils import UserStatusValidator
            validation_result = UserStatusValidator.validate_user_status(user, log_violations=True)
            if not validation_result['is_valid']:
                return {
                    'error': 'User account is not valid',
                    'details': validation_result['error_message']
                }
            
            # Get application
            try:
                if hasattr(Application, 'objects'):
                    application = Application.objects.get(client_id=payload['aud'])
                else:
                    # Fallback for testing
                    application = type('MockApp', (), {'client_id': payload['aud'], 'id': 1})()
            except:
                return {'error': 'Application not found'}
            
            # Check refresh token metadata
            refresh_cache_key = f"{cls.REFRESH_CACHE_PREFIX}{hash(refresh_token)}"
            token_metadata = cache.get(refresh_cache_key)
            
            if token_metadata:
                # Validate device consistency if device_id is present
                device_id = payload.get('device_id')
                if device_id and token_metadata.get('device_id') != device_id:
                    log_security_event(
                        user=user,
                        event_type='refresh_token_device_mismatch',
                        description='Refresh token used from different device',
                        ip_address=get_client_ip(request) if request else None,
                        user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                        metadata={
                            'original_device': token_metadata.get('device_id'),
                            'current_device': device_id
                        }
                    )
                    return {'error': 'Device validation failed'}
            
            # Generate new token pair
            new_tokens = cls.generate_token_pair(
                user=user,
                application=application,
                device_id=payload.get('device_id'),
                request=request
            )
            
            if 'error' in new_tokens:
                return new_tokens
            
            # Invalidate old refresh token
            cls._invalidate_refresh_token(refresh_token)
            
            # Log token refresh
            log_security_event(
                user=user,
                event_type='jwt_token_refreshed',
                description='Access token refreshed using refresh token',
                ip_address=get_client_ip(request) if request else None,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                device_id=payload.get('device_id'),
                metadata={
                    'old_refresh_token_exp': payload.get('exp'),
                    'new_tokens_generated': True
                }
            )
            
            logger.info(f"Refreshed JWT token for user {user.email}")
            return new_tokens
            
        except Exception as e:
            logger.error(f"Error refreshing JWT token: {str(e)}")
            return {'error': 'Token refresh failed'}
    
    @classmethod
    def revoke_refresh_token(cls, refresh_token: str, user=None, request=None) -> bool:
        """
        Revoke a refresh token
        
        Args:
            refresh_token: Refresh token to revoke
            user: User instance (optional, for validation)
            request: HTTP request object (optional)
            
        Returns:
            bool: Success status
        """
        try:
            # Validate refresh token
            payload = validate_jwt_token(refresh_token)
            if not payload:
                return False
            
            # Validate user if provided
            if user and str(user.id) != payload.get('sub'):
                return False
            
            # Invalidate refresh token
            cls._invalidate_refresh_token(refresh_token)
            
            # Log revocation
            if user:
                log_security_event(
                    user=user,
                    event_type='refresh_token_revoked',
                    description='Refresh token revoked',
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                    device_id=payload.get('device_id')
                )
            
            logger.info(f"Revoked refresh token for user {payload.get('sub')}")
            return True
            
        except Exception as e:
            logger.error(f"Error revoking refresh token: {str(e)}")
            return False
    
    @classmethod
    def revoke_all_user_tokens(cls, user, reason="user_request", request=None) -> int:
        """
        Revoke all JWT tokens for a user
        
        Args:
            user: User instance
            reason: Reason for revocation
            request: HTTP request object (optional)
            
        Returns:
            int: Number of tokens revoked
        """
        try:
            # Use the token invalidation service for comprehensive revocation
            result = token_invalidation_service.invalidate_user_tokens(
                user=user,
                reason=reason,
                request=request
            )
            
            return result.get('total_invalidated', 0)
            
        except Exception as e:
            logger.error(f"Error revoking all user tokens: {str(e)}")
            return 0
    
    @classmethod
    def _invalidate_refresh_token(cls, refresh_token: str):
        """Invalidate a refresh token by removing it from cache"""
        try:
            refresh_cache_key = f"{cls.REFRESH_CACHE_PREFIX}{hash(refresh_token)}"
            cache.delete(refresh_cache_key)
            
            # Also add to blacklist
            token_invalidation_service._blacklist_token(refresh_token, "token_refreshed")
            
        except Exception as e:
            logger.error(f"Error invalidating refresh token: {str(e)}")
    
    @classmethod
    def validate_access_token(cls, access_token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Validate an access token
        
        Args:
            access_token: Access token to validate
            
        Returns:
            Tuple of (is_valid, payload)
        """
        try:
            # Check if token is blacklisted
            if token_invalidation_service.is_token_blacklisted(access_token):
                return False, None
            
            # Validate JWT token
            payload = validate_jwt_token(access_token)
            if not payload:
                return False, None
            
            # Check if it's an access token
            if payload.get('token_type') != 'access':
                return False, None
            
            return True, payload
            
        except Exception as e:
            logger.error(f"Error validating access token: {str(e)}")
            return False, None


# Global service instance
jwt_rotation_service = JWTRotationService()
