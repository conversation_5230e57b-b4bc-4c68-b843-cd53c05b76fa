from django.contrib.auth.backends import ModelBackend
from django.utils import timezone
from .models import User
from .auth_utils import UserStatusValidator, log_authentication_attempt
from oauth2_auth.utils import get_client_ip
import logging

logger = logging.getLogger(__name__)


class EmailBackend(ModelBackend):
    def authenticate(self, request, email=None, password=None, **kwargs):
        try:
            user = User.objects.get(email=email)

            # Use centralized user status validation
            validation_result = UserStatusValidator.validate_user_status(
                user, log_violations=True
            )

            if not validation_result["is_valid"]:
                # Log failed authentication attempt with specific error code
                log_authentication_attempt(
                    user=user,
                    success=False,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    error_code=validation_result["error_code"],
                )

                logger.warning(
                    f"Authentication blocked for user {email}: {validation_result['error_message']}"
                )
                return None

            # Verify password
            if user.check_password(password):
                # Log successful authentication
                log_authentication_attempt(
                    user=user,
                    success=True,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                )

                # TODO:: Update last login and online status
                user.last_login = timezone.now()
                user.save(update_fields=["last_login"])

                logger.info(f"Successful authentication for user: {email}")
                return user
            else:
                # Log failed password authentication
                log_authentication_attempt(
                    user=user,
                    success=False,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    error_code="INVALID_PASSWORD",
                )

                logger.warning(f"Failed password authentication for user: {email}")
                return None

        except User.DoesNotExist:
            # Log failed authentication attempt for non-existent user
            log_authentication_attempt(
                user=None,
                success=False,
                method="password",
                ip_address=get_client_ip(request) if request else None,
                user_agent=request.META.get("HTTP_USER_AGENT", "") if request else None,
                error_code="USER_NOT_FOUND",
                additional_info={"attempted_email": email},
            )

            logger.warning(f"Authentication attempt for non-existent user: {email}")
            return None

    def get_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id)

            # Use centralized user status validation for session retrieval
            validation_result = UserStatusValidator.validate_user_status(
                user, log_violations=True
            )

            if not validation_result["is_valid"]:
                logger.warning(
                    f"Session retrieval blocked for user {user.email}: {validation_result['error_message']}"
                )
                return None

            return user
        except User.DoesNotExist:
            return None
