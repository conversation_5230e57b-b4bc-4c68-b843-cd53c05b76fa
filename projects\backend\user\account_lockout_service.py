"""
Account Lockout Service

Handles progressive account lockout logic with the following rules:
- 5 failed attempts: 24-hour lockout
- After 24 hours: 3 more attempts allowed
- Subsequent failures: exponentially increasing lockout duration
- Eventually: permanent lockout requiring admin intervention
"""

import logging
from datetime import timed<PERSON>ta
from django.utils import timezone
from oauth2_auth.config import oauth2_security_config

logger = logging.getLogger(__name__)


class AccountLockoutService:
    """Service for managing account lockout logic"""

    # Use centralized configuration
    INITIAL_FAILED_ATTEMPTS_THRESHOLD = oauth2_security_config.MAX_FAILED_ATTEMPTS
    SECONDARY_FAILED_ATTEMPTS_THRESHOLD = 3  # Keep this as is for now
    INITIAL_LOCKOUT_HOURS = oauth2_security_config.LOCKOUT_DURATIONS["first"]
    PERMANENT_LOCKOUT_THRESHOLD = 5  # Keep this as is for now
    FAILED_ATTEMPT_WINDOW_HOURS = oauth2_security_config.FAILED_ATTEMPT_WINDOW_HOURS

    @classmethod
    def handle_failed_login(cls, user, request=None):
        """
        Handle a failed login attempt and apply lockout logic

        Args:
            user: User instance
            request: HTTP request object (optional, for logging)

        Returns:
            dict: Contains lockout status and details
        """
        from oauth2_auth.utils import log_security_event, get_client_ip

        # Get client info for logging
        client_ip = get_client_ip(request) if request else None
        user_agent = request.META.get("HTTP_USER_AGENT", "") if request else ""

        # Check if we need to reset failed attempts due to time window
        current_time = timezone.now()
        if user.last_failed_login:
            time_since_last_failure = current_time - user.last_failed_login
            if time_since_last_failure > timedelta(
                hours=cls.FAILED_ATTEMPT_WINDOW_HOURS
            ):
                # Reset failed attempts if outside the time window
                user.failed_login_attempts = 0
                logger.info(
                    f"Reset failed attempts for {user.email} due to time window expiry"
                )

        # Increment failed attempts
        user.failed_login_attempts += 1
        user.last_failed_login = current_time

        # Reset failed attempts if outside the time window
        if not cls._is_within_attempt_window(user):
            user.failed_login_attempts = 0
            user.last_failed_login = None
            logger.info(
                f"Reset failed attempts for {user.email} due to time window expiry"
            )

        lockout_info = {
            "is_locked": False,
            "lockout_duration": None,
            "attempts_remaining": None,
            "is_permanent": False,
            "message": None,
        }

        # Check if account should be locked
        if cls._should_lock_account(user):
            lockout_duration = cls._calculate_lockout_duration(user)

            if lockout_duration == "permanent":
                user.is_permanently_locked = True
                user.lockout_until = None
                lockout_info.update(
                    {
                        "is_locked": True,
                        "is_permanent": True,
                        "message": "Account permanently locked. Contact administrator to unlock.",
                    }
                )

                # Log permanent lockout event
                log_security_event(
                    user=user,
                    event_type="account_permanently_locked",
                    description="Account permanently locked due to repeated failed login attempts",
                    ip_address=client_ip,
                    user_agent=user_agent,
                    metadata={
                        "failed_attempts": user.failed_login_attempts,
                        "lockout_count": user.lockout_count,
                    },
                )

            else:
                user.lockout_until = timezone.now() + lockout_duration
                user.lockout_count += 1
                lockout_info.update(
                    {
                        "is_locked": True,
                        "lockout_duration": lockout_duration.total_seconds(),
                        "message": f"Account locked for {cls._format_duration(lockout_duration)} due to failed login attempts.",
                    }
                )

                # Log temporary lockout event
                log_security_event(
                    user=user,
                    event_type="account_locked",
                    description=f"Account locked for {cls._format_duration(lockout_duration)}",
                    ip_address=client_ip,
                    user_agent=user_agent,
                    metadata={
                        "failed_attempts": user.failed_login_attempts,
                        "lockout_count": user.lockout_count,
                        "lockout_duration_seconds": lockout_duration.total_seconds(),
                    },
                )

        else:
            # Account not locked yet, calculate remaining attempts
            remaining = cls._get_remaining_attempts(user)
            lockout_info.update(
                {
                    "attempts_remaining": remaining,
                    "message": f"{remaining} attempts remaining before account lockout.",
                }
            )

        # Save user changes
        user.save(
            update_fields=[
                "failed_login_attempts",
                "last_failed_login",
                "lockout_until",
                "lockout_count",
                "is_permanently_locked",
            ]
        )

        # Log failed login attempt
        log_security_event(
            user=user,
            event_type="failed_login",
            description="Failed login attempt",
            ip_address=client_ip,
            user_agent=user_agent,
            metadata={
                "failed_attempts": user.failed_login_attempts,
                "is_locked": lockout_info["is_locked"],
            },
        )

        return lockout_info

    @classmethod
    def check_account_lockout(cls, user):
        """
        Check if account is currently locked

        Args:
            user: User instance

        Returns:
            dict: Lockout status information
        """
        if user.is_permanently_locked:
            return {
                "is_locked": True,
                "is_permanent": True,
                "remaining_time": None,
                "message": "Account permanently locked. Contact administrator to unlock.",
            }

        if user.lockout_until and timezone.now() < user.lockout_until:
            remaining_seconds = (user.lockout_until - timezone.now()).total_seconds()
            return {
                "is_locked": True,
                "is_permanent": False,
                "remaining_time": remaining_seconds,
                "message": f"Account locked. Try again in {cls._format_seconds(remaining_seconds)}.",
            }

        # Check if lockout has expired and reset attempts if needed
        if user.lockout_until and timezone.now() >= user.lockout_until:
            cls._reset_attempts_after_lockout(user)

        # Also check if failed attempts should be reset due to time window
        elif user.last_failed_login and user.failed_login_attempts > 0:
            time_since_last_failure = timezone.now() - user.last_failed_login
            if time_since_last_failure > timedelta(
                hours=cls.FAILED_ATTEMPT_WINDOW_HOURS
            ):
                # Reset failed attempts if outside the time window
                user.failed_login_attempts = 0
                user.last_failed_login = None
                user.save(update_fields=["failed_login_attempts", "last_failed_login"])
                logger.info(
                    f"Reset failed attempts for {user.email} due to time window expiry during status check"
                )

        return {
            "is_locked": False,
            "is_permanent": False,
            "remaining_time": 0,
            "message": None,
        }

    @classmethod
    def _should_lock_account(cls, user):
        """Determine if account should be locked based on failed attempts"""
        if user.lockout_count == 0:
            # First lockout cycle - 5 attempts
            return user.failed_login_attempts >= cls.INITIAL_FAILED_ATTEMPTS_THRESHOLD
        else:
            # Subsequent cycles - 3 attempts after each unlock
            return user.failed_login_attempts >= cls.SECONDARY_FAILED_ATTEMPTS_THRESHOLD

    @classmethod
    def _get_remaining_attempts(cls, user):
        """Get remaining attempts before lockout"""
        if user.lockout_count == 0:
            return cls.INITIAL_FAILED_ATTEMPTS_THRESHOLD - user.failed_login_attempts
        else:
            return cls.SECONDARY_FAILED_ATTEMPTS_THRESHOLD - user.failed_login_attempts

    @classmethod
    def _calculate_lockout_duration(cls, user):
        """Calculate lockout duration based on lockout history"""
        if user.lockout_count >= cls.PERMANENT_LOCKOUT_THRESHOLD:
            return "permanent"

        if user.lockout_count == 0:
            # First lockout: 24 hours
            return timedelta(hours=cls.INITIAL_LOCKOUT_HOURS)
        else:
            # Progressive lockout: exponential backoff
            # 2nd lockout: 48 hours, 3rd: 96 hours, 4th: 192 hours, 5th: permanent
            hours = cls.INITIAL_LOCKOUT_HOURS * (2**user.lockout_count)
            return timedelta(hours=hours)

    @classmethod
    def _reset_attempts_after_lockout(cls, user):
        """Reset failed attempts after lockout period expires"""
        user.failed_login_attempts = 0
        user.lockout_until = None
        user.last_failed_login = None
        user.save(
            update_fields=[
                "failed_login_attempts",
                "lockout_until",
                "last_failed_login",
            ]
        )

    @classmethod
    def _is_within_attempt_window(cls, user):
        """Check if current time is within the failed attempt window"""
        if not user.last_failed_login:
            return True  # First attempt is always within window

        time_since_last_failure = timezone.now() - user.last_failed_login
        return time_since_last_failure <= timedelta(
            hours=cls.FAILED_ATTEMPT_WINDOW_HOURS
        )

    @classmethod
    def get_time_window_remaining(cls, user):
        """Get remaining time in the current attempt window"""
        if not user.last_failed_login:
            return cls.FAILED_ATTEMPT_WINDOW_HOURS * 3600  # Full window in seconds

        time_since_last_failure = timezone.now() - user.last_failed_login
        window_duration = timedelta(hours=cls.FAILED_ATTEMPT_WINDOW_HOURS)

        if time_since_last_failure >= window_duration:
            return 0  # Window has expired

        remaining = window_duration - time_since_last_failure
        return remaining.total_seconds()

    @classmethod
    def _format_duration(cls, duration):
        """Format timedelta for user-friendly display"""
        if isinstance(duration, str):
            return duration

        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60

        if hours > 0:
            return f"{hours} hour{'s' if hours != 1 else ''}"
        elif minutes > 0:
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
        else:
            return "less than a minute"

    @classmethod
    def _format_seconds(cls, seconds):
        """Format seconds for user-friendly display"""
        total_seconds = int(seconds)
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60

        if hours > 0:
            return f"{hours} hour{'s' if hours != 1 else ''} and {minutes} minute{'s' if minutes != 1 else ''}"
        elif minutes > 0:
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
        else:
            return "less than a minute"

    @classmethod
    def unlock_account_admin(cls, user, admin_user=None):
        """
        Admin function to unlock an account

        Args:
            user: User instance to unlock
            admin_user: Admin user performing the unlock (optional)

        Returns:
            bool: Success status
        """
        from oauth2_auth.utils import log_security_event

        try:
            user.unlock_account()

            # Log admin unlock event
            log_security_event(
                user=user,
                event_type="account_unlocked",
                description="Account unlocked by administrator",
                metadata={
                    "admin_user": admin_user.email if admin_user else "System",
                    "was_permanently_locked": user.is_permanently_locked,
                    "previous_lockout_count": user.lockout_count,
                },
            )

            logger.info(
                f"Account {user.email} unlocked by admin {admin_user.email if admin_user else 'System'}"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to unlock account {user.email}: {str(e)}")
            return False
