import jwt
import hashlib
import secrets
import logging
from datetime import timedelta
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import SecurityEvent, DeviceToken

User = get_user_model()
logger = logging.getLogger(__name__)


def generate_jwt_token(user, application, scope=None, expires_in=3600, device_id=None, token_type='access'):
    """
    Generate a JWT token for the given user and application
    Supports both HMAC (HS256) and RSA (RS256) algorithms
    Enhanced with device binding and token rotation support
    """
    try:
        now = timezone.now()

        # Generate key ID for token header
        import hashlib
        key_id = hashlib.sha256(settings.SECRET_KEY.encode()).hexdigest()[:16]

        payload = {
            'sub': str(user.id),
            'iss': settings.OAUTH2_PROVIDER.get('ISSUER', 'agritram'),
            'aud': application.client_id if hasattr(application, 'client_id') else str(application),
            'iat': int(now.timestamp()),
            'exp': int((now + timedelta(seconds=expires_in)).timestamp()),
            'email': user.email,
            'name': user.name,
            'role': user.role,
            'token_type': token_type,  # Add token type for rotation support
            # Add user status information to JWT claims
            'user_status': {
                'is_active': user.is_active,
                'is_deleted': user.is_deleted,
                'is_mail_verified': user.is_mail_verified,
                'is_account_locked': user.is_account_locked(),
                'is_permanently_locked': user.is_permanently_locked,
            }
        }

        # Add device binding if device_id is provided
        if device_id:
            payload['device_id'] = device_id

        if scope:
            payload['scope'] = scope

        # Check if RSA private key is configured
        rsa_private_key = settings.OAUTH2_PROVIDER.get('OIDC_RSA_PRIVATE_KEY')

        if rsa_private_key:
            # Use RSA algorithm (RS256)
            try:
                from cryptography.hazmat.primitives.serialization import load_pem_private_key
                private_key = load_pem_private_key(
                    rsa_private_key.encode(),
                    password=None,
                )

                token = jwt.encode(
                    payload,
                    private_key,
                    algorithm='RS256',
                    headers={'kid': key_id}
                )
            except Exception as rsa_error:
                logger.warning(f"RSA token generation failed: {rsa_error}, falling back to HMAC")
                # Fall back to HMAC
                token = jwt.encode(
                    payload,
                    settings.SECRET_KEY,
                    algorithm='HS256',
                    headers={'kid': key_id}
                )
        else:
            # Use HMAC algorithm (HS256)
            token = jwt.encode(
                payload,
                settings.SECRET_KEY,
                algorithm='HS256',
                headers={'kid': key_id}
            )

        return token
    except Exception as e:
        logger.error(f"JWT token generation error: {str(e)}")
        return None


def validate_jwt_token(token):
    """
    Validate and decode a JWT token
    Supports both HMAC (HS256) and RSA (RS256) algorithms
    """
    try:
        # First, decode the header to check the algorithm
        header = jwt.get_unverified_header(token)
        algorithm = header.get('alg', 'HS256')

        if algorithm == 'RS256':
            # RSA-based JWT validation
            rsa_private_key = settings.OAUTH2_PROVIDER.get('OIDC_RSA_PRIVATE_KEY')
            if rsa_private_key:
                try:
                    from cryptography.hazmat.primitives.serialization import load_pem_private_key
                    private_key = load_pem_private_key(
                        rsa_private_key.encode(),
                        password=None,
                    )
                    public_key = private_key.public_key()

                    payload = jwt.decode(
                        token,
                        public_key,
                        algorithms=['RS256']
                    )
                    return payload
                except Exception as rsa_error:
                    logger.warning(f"RSA token validation failed: {rsa_error}, trying HMAC")
                    # Fall through to HMAC validation

        # HMAC-based JWT validation (default)
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=['HS256']
        )
        return payload

    except jwt.ExpiredSignatureError:
        logger.warning("JWT token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {str(e)}")
        return None

def log_security_event(event_type, description, user=None, ip_address=None,
                      user_agent=None, device_id=None, application=None, metadata=None, request=None):
    """
    Log a security event using the comprehensive security logger
    """
    try:
        from .security_logger import security_logger

        # Prepare metadata
        event_metadata = metadata or {}
        if application:
            event_metadata['application_id'] = application.id
            event_metadata['client_id'] = application.client_id

        return security_logger.log_event(
            event_type=event_type,
            description=description,
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            device_id=device_id,
            metadata=event_metadata,
            request=request
        )

    except Exception as e:
        logger.error(f"Failed to log security event: {str(e)}")
        return None


def generate_device_fingerprint(request):
    """
    Generate a device fingerprint based on request headers
    """
    try:
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        accept_encoding = request.META.get('HTTP_ACCEPT_ENCODING', '')
        
        fingerprint_data = f"{user_agent}|{accept_language}|{accept_encoding}"
        fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()
        
        return fingerprint
    except Exception as e:
        logger.error(f"Device fingerprint generation error: {str(e)}")
        return None


def register_device(user, device_id, device_name, device_type, request):
    """
    Register a new device for the user
    """
    try:
        fingerprint = generate_device_fingerprint(request)
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        device, created = DeviceToken.objects.get_or_create(
            user=user,
            device_id=device_id,
            defaults={
                'device_name': device_name,
                'device_type': device_type,
                'fingerprint': fingerprint,
                'ip_address': ip_address,
                'user_agent': user_agent,
            }
        )
        
        if not created:
            # Update existing device
            device.device_name = device_name
            device.device_type = device_type
            device.fingerprint = fingerprint
            device.ip_address = ip_address
            device.user_agent = user_agent
            device.last_seen = timezone.now()
            device.save()
        
        log_security_event(
            event_type='device_registered',
            description=f'Device registered: {device_name}',
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            device_id=device_id,
            metadata={
                'device_type': device_type,
                'fingerprint': fingerprint,
                'created': created
            }
        )
        
        return device
    except Exception as e:
        logger.error(f"Device registration error: {str(e)}")
        return None


def get_client_ip(request):
    """
    Get client IP address from request
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def generate_secure_token(length=32):
    """
    Generate a cryptographically secure random token
    """
    return secrets.token_urlsafe(length)


def calculate_fingerprint_similarity(fingerprint1, fingerprint2):
    """
    Calculate similarity between two device fingerprints
    Returns a score between 0 and 1 (1 being identical)
    """
    if not fingerprint1 or not fingerprint2:
        return 0.0

    if fingerprint1 == fingerprint2:
        return 1.0

    # Simple Jaccard similarity for fingerprint comparison
    # Split fingerprints into components and compare
    try:
        components1 = set(fingerprint1.split('|'))
        components2 = set(fingerprint2.split('|'))

        intersection = len(components1.intersection(components2))
        union = len(components1.union(components2))

        if union == 0:
            return 0.0

        return intersection / union
    except Exception as e:
        logger.error(f"Error calculating fingerprint similarity: {str(e)}")
        return 0.0


def validate_device_security(device_info, fingerprint, ip_address, user_agent):
    """
    Validate device security based on multiple factors
    Returns a security score between 0 and 100
    """
    score = 50  # Base score

    try:
        # Check for consistent user agent
        if device_info.get('user_agent') == user_agent:
            score += 15

        # Check for reasonable screen resolution
        screen_res = device_info.get('screen_resolution', '')
        if screen_res and 'x' in screen_res:
            try:
                width, height = map(int, screen_res.split('x'))
                if 800 <= width <= 4000 and 600 <= height <= 3000:
                    score += 10
            except ValueError:
                score -= 5

        # Check for valid timezone
        timezone = device_info.get('timezone', '')
        if timezone and len(timezone) > 3:
            score += 10

        # Check for valid language
        language = device_info.get('language', '')
        if language and len(language) >= 2:
            score += 5

        # Check for valid platform
        platform = device_info.get('platform', '')
        if platform and any(p in platform.lower() for p in ['win', 'mac', 'linux', 'android', 'ios']):
            score += 10

        # Fingerprint quality check
        if fingerprint and len(fingerprint) > 10:
            score += 10

        return min(max(score, 0), 100)
    except Exception as e:
        logger.error(f"Error validating device security: {str(e)}")
        return 30  # Low security score on error


def hash_token(token):
    """
    Hash a token for secure storage
    """
    return hashlib.sha256(token.encode()).hexdigest()

def send_otp_email(user, otp_code, device_name, ip_address):
    """
    Send OTP verification email to user using email service
    """
    try:
        from .email_service import email_service
        return email_service.send_otp_verification(user, otp_code, device_name, ip_address)
    except Exception as e:
        logger.error(f"Failed to send OTP email: {str(e)}")
        return False


def send_security_alert_email(user, alert_type, details):
    """
    Send security alert email to user using email service
    """
    try:
        from .email_service import email_service
        return email_service.send_security_alert(user, alert_type, details)
    except Exception as e:
        logger.error(f"Failed to send security alert email: {str(e)}")
        return False


def detect_suspicious_activity(user, request, event_type):
    """
    Detect suspicious activity patterns
    """
    try:
        client_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        # Check for multiple failed login attempts
        if event_type == 'failed_login':
            recent_failures = SecurityEvent.objects.filter(
                user=user,
                event_type='failed_login',
                created_at__gte=timezone.now() - timedelta(minutes=15)
            ).count()

            if recent_failures >= 5:
                send_security_alert_email(
                    user,
                    "Multiple Failed Login Attempts",
                    f"5 or more failed login attempts in 15 minutes from IP: {client_ip}"
                )
                return True

        # Check for login from new location/IP
        if event_type == 'login':
            recent_logins = SecurityEvent.objects.filter(
                user=user,
                event_type='login',
                ip_address=client_ip,
                created_at__gte=timezone.now() - timedelta(days=30)
            ).exists()

            if not recent_logins:
                send_security_alert_email(
                    user,
                    "Login from New Location",
                    f"Login detected from new IP address: {client_ip}"
                )

        return False

    except Exception as e:
        logger.error(f"Suspicious activity detection error: {str(e)}")
        return False
